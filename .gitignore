#Custom WP git ignore for <PERSON><PERSON> written by <PERSON>
#Do not change unless you know what you are doing. 

#
# If all files excluded and you will include only specific sub-directories
# the parent path must matched before.
#

/*
# ignore everything in the root except the "wp-content" directory.
!wp-content/

# ignore everything in the "wp-content" directory, except:
# mu-plugins, plugins, and themes directories
wp-content/*
!wp-content/plugins/
!wp-content/themes/

# ignore all mu-plugins, plugins, and themes
# unless explicitly whitelisted at the end of this file
wp-content/themes/*
wp-content/plugins/*

# ignore index.php in plugins folder
wp-content/plugins/index.php

# ignore all files starting with . or ~
.*
~*

# ignore node dependency directories (used by grunt)
node_modules/

# ignore OS generated files
ehthumbs.db
Thumbs.db

# ignore Editor files
*.sublime-project
*.sublime-workspace
*.komodoproject
*.codekit3
.idea

# ignore log files and databases
*.log
*.sql
*.sqlite

# ignore compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# ignore packaged files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# -------------------------
# BEGIN Whitelisted Files
# -------------------------

# track these files, if they exist
!.gitignore
!.editorconfig
!.scss-lint.yml
!README.md
!CHANGELOG.md
!composer.json

# track these themes
!wp-content/themes/parent-theme/
!wp-content/themes/weinsteinjcc/
!wp-content/themes/weinsteinjcc/*
!wp-content/themes/parent-theme/*

#track these plugins
!wp-content/plugins/yobo-ticket/
!wp-content/plugins/yobo-ticket/*
!wp-content/plugins/weinstein-yobo/
!wp-content/plugins/weinstein-yobo/*
